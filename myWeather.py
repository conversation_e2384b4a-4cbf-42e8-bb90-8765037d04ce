import openmeteo_requests
import requests
import datetime
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry
import subprocess
import math

# Setup the Open-Meteo API client with cache and retry on error
cache_session = requests.Session()

# Configure retry strategy
retry_strategy = Retry(
    total=3,  # Maximum number of retries
    backoff_factor=1,  # Exponential backoff factor (1 means 1s, 2s, 4s...)
    status_forcelist=[429, 500, 502, 503, 504]  # HTTP status codes to retry on
)

adapter = HTTPAdapter(max_retries=retry_strategy)
cache_session.mount("http://", adapter)
cache_session.mount("https://", adapter)

def calculate_heat_index(temp_f, humidity):
    """
    Calculate heat index using the National Weather Service formula.

    Args:
        temp_f (float): Temperature in Fahrenheit
        humidity (float): Relative humidity as a percentage (0-100)

    Returns:
        float: Heat index in Fahrenheit, rounded to nearest integer
    """
    # If temperature is below 80°F, heat index is just the temperature
    if temp_f < 80:
        return temp_f

    # Constants for the heat index formula
    c1 = -42.379
    c2 = 2.04901523
    c3 = 10.14333127
    c4 = -0.22475541
    c5 = -6.83783e-3
    c6 = -5.481717e-2
    c7 = 1.22874e-3
    c8 = 8.5282e-4
    c9 = -1.99e-6

    # Calculate heat index using the full regression equation
    hi = (c1 + (c2 * temp_f) + (c3 * humidity) + (c4 * temp_f * humidity) +
          (c5 * temp_f * temp_f) + (c6 * humidity * humidity) +
          (c7 * temp_f * temp_f * humidity) + (c8 * temp_f * humidity * humidity) +
          (c9 * temp_f * temp_f * humidity * humidity))

    return round(hi)


# Make sure all required weather variables are included for the first location
#latitude = 52.52
#longitude = 13.41
#latitude = 34.0522 # Example: Los Angeles latitude
#longitude = -118.2437 # Example: Los Angeles longitude
latitude = 27.9239
longitude = -80.5456

# {{change 2}}
url = "https://api.open-meteo.com/v1/forecast"
params = {
	"latitude": latitude,
	"longitude": longitude,
	"current": ["temperature_2m", "relative_humidity_2m", "wind_speed_10m", "wind_direction_10m", "uv_index"],
	"timezone": "auto"
}

try:
    responses = cache_session.get(url, params = params, timeout = 5) # Changed retry_session to cache_session
    responses.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
    #print(f"response status code: {responses.status_code}")
    data = responses.json()

    current = data["current"]
    temperature = current["temperature_2m"]
    # Convert Celsius to Fahrenheit
    temperature_f = round((temperature * 9/5) + 32)

    # {{change 3}}
    humidity = current["relative_humidity_2m"]
    wind_speed = current["wind_speed_10m"]
    wind_direction = current["wind_direction_10m"]
    uv_index = current["uv_index"]

    # Calculate heat index
    heat_index = calculate_heat_index(temperature_f, humidity)
    
    # Map wind direction degrees to cardinal direction
    directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]
    wind_cardinal = directions[round(wind_direction / 45) % 8]

    # Format the message for the notification
    # Include heat index if it's significantly different from actual temperature
    if heat_index > temperature_f + 2:  # Only show heat index if it's at least 3°F higher
        notification_message = f"⛅️ UV{uv_index} {temperature_f}°F (feels {heat_index}°F) {humidity}% {wind_cardinal}{wind_speed}mph"
    else:
        notification_message = f"⛅️ UV{uv_index} {temperature_f}°F {humidity}% {wind_cardinal}{wind_speed}mph"

    # Use notify-send to display the notification
    # The first argument is the application name, the second is the message
    try:
        subprocess.run(["notify-send", "Weather Update", notification_message], check=True)
        # Remove or comment out the print statement if you only want notifications
        # print(notification_message)
    except subprocess.CalledProcessError as e:
        print(f"Error sending notification: {e}")
    except FileNotFoundError:
        print("Error: 'notify-send' command not found. Make sure it's installed and in your PATH.")

except requests.exceptions.RequestException as e:
    # You might want to send an error notification too
    error_message = f"An error occurred fetching weather: {e}"
    try:
        subprocess.run(["notify-send", "Weather Error", error_message], check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print(error_message) # Fallback to print if notification fails
except KeyError as e:
    # Handle KeyError with a notification
    error_message = f"KeyError: {e}. Check the API response."
    try:
        subprocess.run(["notify-send", "Weather Error", error_message], check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print(error_message) # Fallback to print
except Exception as e:
    # Handle other exceptions with a notification
    error_message = f"An unexpected error occurred: {e}"
    try:
        subprocess.run(["notify-send", "Weather Error", error_message], check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print(error_message) # Fallback to print